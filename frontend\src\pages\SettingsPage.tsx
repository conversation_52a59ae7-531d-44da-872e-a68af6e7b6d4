import React, { useState } from 'react'
import { Save, User, Key, Palette, Bell, Shield } from 'lucide-react'

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile')
  const [settings, setSettings] = useState({
    profile: {
      username: 'user123',
      email: '<EMAIL>',
      fullName: '张三',
      avatar: ''
    },
    ai: {
      defaultModel: 'gpt-4',
      temperature: 0.7,
      maxTokens: 4096,
      enableStreaming: true
    },
    appearance: {
      theme: 'light',
      language: 'zh-CN',
      fontSize: 'medium'
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: false,
      taskReminders: true
    }
  })

  const tabs = [
    { id: 'profile', name: '个人资料', icon: User },
    { id: 'ai', name: 'AI设置', icon: Key },
    { id: 'appearance', name: '外观', icon: Palette },
    { id: 'notifications', name: '通知', icon: Bell },
    { id: 'security', name: '安全', icon: Shield }
  ]

  const handleSave = () => {
    // 保存设置逻辑
    console.log('保存设置:', settings)
  }

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-foreground">个人资料</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">用户名</label>
          <input
            type="text"
            value={settings.profile.username}
            className="w-full p-2 border rounded-lg bg-background text-foreground"
            onChange={(e) => setSettings({
              ...settings,
              profile: { ...settings.profile, username: e.target.value }
            })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">邮箱</label>
          <input
            type="email"
            value={settings.profile.email}
            className="w-full p-2 border rounded-lg bg-background text-foreground"
            onChange={(e) => setSettings({
              ...settings,
              profile: { ...settings.profile, email: e.target.value }
            })}
          />
        </div>
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-foreground mb-2">姓名</label>
          <input
            type="text"
            value={settings.profile.fullName}
            className="w-full p-2 border rounded-lg bg-background text-foreground"
            onChange={(e) => setSettings({
              ...settings,
              profile: { ...settings.profile, fullName: e.target.value }
            })}
          />
        </div>
      </div>
    </div>
  )

  const renderAISettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-foreground">AI模型设置</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">默认模型</label>
          <select
            value={settings.ai.defaultModel}
            className="w-full p-2 border rounded-lg bg-background text-foreground"
            onChange={(e) => setSettings({
              ...settings,
              ai: { ...settings.ai, defaultModel: e.target.value }
            })}
          >
            <option value="gpt-4">GPT-4 (OpenAI)</option>
            <option value="claude-3.5">Claude 3.5 (Anthropic)</option>
            <option value="gemini-pro">Gemini Pro (Google)</option>
            <option value="llama3">Llama 3 (本地)</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            创造性 (Temperature): {settings.ai.temperature}
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={settings.ai.temperature}
            className="w-full"
            onChange={(e) => setSettings({
              ...settings,
              ai: { ...settings.ai, temperature: parseFloat(e.target.value) }
            })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">最大令牌数</label>
          <input
            type="number"
            value={settings.ai.maxTokens}
            className="w-full p-2 border rounded-lg bg-background text-foreground"
            onChange={(e) => setSettings({
              ...settings,
              ai: { ...settings.ai, maxTokens: parseInt(e.target.value) }
            })}
          />
        </div>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={settings.ai.enableStreaming}
            onChange={(e) => setSettings({
              ...settings,
              ai: { ...settings.ai, enableStreaming: e.target.checked }
            })}
          />
          <label className="text-sm text-foreground">启用流式响应</label>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileSettings()
      case 'ai':
        return renderAISettings()
      case 'appearance':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-foreground">外观设置</h3>
            <p className="text-muted-foreground">外观设置功能开发中...</p>
          </div>
        )
      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-foreground">通知设置</h3>
            <p className="text-muted-foreground">通知设置功能开发中...</p>
          </div>
        )
      case 'security':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-foreground">安全设置</h3>
            <p className="text-muted-foreground">安全设置功能开发中...</p>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-foreground">设置</h1>
        <button
          onClick={handleSave}
          className="flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
        >
          <Save className="h-4 w-4 mr-2" />
          保存设置
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 侧边栏导航 */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                    ${activeTab === tab.id
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                    }
                  `}
                >
                  <Icon className="h-4 w-4 mr-3" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* 主内容区域 */}
        <div className="lg:col-span-3">
          <div className="bg-card rounded-lg border p-6">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage
