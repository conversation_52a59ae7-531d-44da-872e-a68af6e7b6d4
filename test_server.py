#!/usr/bin/env python3
"""
CloudAI Assistant 快速测试服务器
只需要Python即可运行的简化版本
"""
import json
import os
from datetime import datetime
from typing import Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

# 数据模型
class ChatMessage(BaseModel):
    content: str
    model: str = "test-model"

class ChatResponse(BaseModel):
    content: str
    model: str
    timestamp: str
    tokens_used: int

# 创建FastAPI应用
app = FastAPI(
    title="CloudAI Assistant Test Server",
    description="CloudAI Assistant 快速测试服务器",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据存储
conversations = []
files = []
tasks = []

# 模拟AI响应函数
def generate_ai_response(user_message: str) -> str:
    """生成模拟的AI响应"""
    responses = {
        "你好": "您好！我是CloudAI Assistant，很高兴为您服务！",
        "介绍": "我是一个功能强大的AI助手，可以帮您进行对话、代码分析、文件处理和任务管理。",
        "功能": "我的主要功能包括：\n1. 智能对话\n2. 代码分析和生成\n3. 文件管理\n4. 任务规划\n5. 多模型集成",
        "帮助": "您可以：\n- 与我自由对话\n- 询问编程问题\n- 上传和管理文件\n- 创建和跟踪任务\n- 调整AI模型设置",
    }
    
    # 简单的关键词匹配
    for keyword, response in responses.items():
        if keyword in user_message:
            return response
    
    # 默认响应
    return f"我收到了您的消息：「{user_message}」\n\n这是一个测试响应。在完整版本中，这里会调用真实的AI模型（如GPT-4、Claude等）来生成智能回复。\n\n您可以尝试输入：你好、介绍、功能、帮助 等关键词来体验不同的响应。"

# API路由
@app.get("/", response_class=HTMLResponse)
async def root():
    """返回简单的测试页面"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CloudAI Assistant 测试服务器</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .chat-container { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
            .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
            .user-message { background-color: #007bff; color: white; text-align: right; }
            .ai-message { background-color: #f8f9fa; border-left: 4px solid #007bff; }
            .input-area { display: flex; gap: 10px; }
            .input-area input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
            .input-area button { padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
            .status { padding: 10px; background-color: #d4edda; border-radius: 4px; margin-bottom: 20px; }
            .api-info { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 CloudAI Assistant 测试服务器</h1>
            <p>快速测试版本 - 体验AI助手的基本功能</p>
        </div>
        
        <div class="status">
            ✅ 服务器运行正常 | 📡 API地址: <a href="/docs" target="_blank">/docs</a> | 🔧 健康检查: <a href="/health" target="_blank">/health</a>
        </div>
        
        <div class="chat-container">
            <h3>💬 AI对话测试</h3>
            <div id="messages"></div>
            <div class="input-area">
                <input type="text" id="messageInput" placeholder="输入您的消息..." onkeypress="if(event.key==='Enter') sendMessage()">
                <button onclick="sendMessage()">发送</button>
            </div>
        </div>
        
        <div class="api-info">
            <h3>🔧 API测试信息</h3>
            <p><strong>聊天API:</strong> POST /api/v1/chat</p>
            <p><strong>健康检查:</strong> GET /health</p>
            <p><strong>API文档:</strong> <a href="/docs" target="_blank">Swagger UI</a></p>
            <p><strong>测试命令:</strong></p>
            <code>curl -X POST http://localhost:8000/api/v1/chat -H "Content-Type: application/json" -d '{"content": "你好"}'</code>
        </div>
        
        <script>
            async function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (!message) return;
                
                const messagesDiv = document.getElementById('messages');
                
                // 显示用户消息
                messagesDiv.innerHTML += `<div class="message user-message">👤 ${message}</div>`;
                input.value = '';
                
                // 显示加载状态
                messagesDiv.innerHTML += `<div class="message ai-message" id="loading">🤖 AI正在思考...</div>`;
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
                
                try {
                    const response = await fetch('/api/v1/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ content: message })
                    });
                    
                    const data = await response.json();
                    
                    // 移除加载状态
                    document.getElementById('loading').remove();
                    
                    // 显示AI响应
                    messagesDiv.innerHTML += `<div class="message ai-message">🤖 ${data.content.replace(/\\n/g, '<br>')}</div>`;
                } catch (error) {
                    document.getElementById('loading').innerHTML = '🤖 ❌ 连接失败，请检查服务器状态';
                }
                
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
            
            // 页面加载时显示欢迎消息
            document.addEventListener('DOMContentLoaded', function() {
                const messagesDiv = document.getElementById('messages');
                messagesDiv.innerHTML = `<div class="message ai-message">🤖 欢迎使用CloudAI Assistant测试服务器！<br>您可以尝试输入：你好、介绍、功能、帮助</div>`;
            });
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "CloudAI Assistant Test Server",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.post("/api/v1/chat", response_model=ChatResponse)
async def chat(message: ChatMessage):
    """聊天API"""
    try:
        # 生成AI响应
        ai_response = generate_ai_response(message.content)
        
        # 保存对话历史
        conversation = {
            "user_message": message.content,
            "ai_response": ai_response,
            "timestamp": datetime.now().isoformat(),
            "model": message.model
        }
        conversations.append(conversation)
        
        return ChatResponse(
            content=ai_response,
            model=message.model,
            timestamp=datetime.now().isoformat(),
            tokens_used=len(message.content) + len(ai_response)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理消息时出错: {str(e)}")

@app.get("/api/v1/conversations")
async def get_conversations():
    """获取对话历史"""
    return {"conversations": conversations[-10:]}  # 返回最近10条对话

@app.get("/api/v1/stats")
async def get_stats():
    """获取统计信息"""
    return {
        "total_conversations": len(conversations),
        "total_files": len(files),
        "total_tasks": len(tasks),
        "server_uptime": "运行中",
        "available_models": ["test-model", "gpt-4", "claude-3.5", "gemini-pro"]
    }

# 模拟文件管理API
@app.post("/api/v1/files/upload")
async def upload_file():
    """模拟文件上传"""
    file_info = {
        "id": len(files) + 1,
        "name": f"test_file_{len(files) + 1}.txt",
        "size": "1.2 KB",
        "upload_time": datetime.now().isoformat(),
        "status": "uploaded"
    }
    files.append(file_info)
    return {"message": "文件上传成功", "file": file_info}

@app.get("/api/v1/files")
async def get_files():
    """获取文件列表"""
    return {"files": files}

# 模拟任务管理API
@app.post("/api/v1/tasks")
async def create_task(task_data: dict):
    """创建任务"""
    task = {
        "id": len(tasks) + 1,
        "title": task_data.get("title", "新任务"),
        "description": task_data.get("description", ""),
        "status": "not_started",
        "created_at": datetime.now().isoformat()
    }
    tasks.append(task)
    return {"message": "任务创建成功", "task": task}

@app.get("/api/v1/tasks")
async def get_tasks():
    """获取任务列表"""
    return {"tasks": tasks}

if __name__ == "__main__":
    print("🚀 启动CloudAI Assistant测试服务器...")
    print("📡 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("💬 测试页面: http://localhost:8000")
    print("🔧 健康检查: http://localhost:8000/health")
    print("\n按 Ctrl+C 停止服务器")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    )
