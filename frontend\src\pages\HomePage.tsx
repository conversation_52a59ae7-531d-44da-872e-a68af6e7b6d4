import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  MessageSquare, 
  FileText, 
  CheckSquare, 
  Settings,
  Sparkles,
  Code,
  Brain,
  Zap
} from 'lucide-react'

const HomePage: React.FC = () => {
  const features = [
    {
      icon: <MessageSquare className="h-8 w-8" />,
      title: "智能对话",
      description: "与多个AI模型进行自然对话，获得专业的回答和建议",
      link: "/chat",
      color: "text-blue-500"
    },
    {
      icon: <Code className="h-8 w-8" />,
      title: "代码分析",
      description: "智能代码理解、重构和生成，提升开发效率",
      link: "/chat",
      color: "text-green-500"
    },
    {
      icon: <FileText className="h-8 w-8" />,
      title: "文件管理",
      description: "上传、管理和处理各种类型的文件",
      link: "/files",
      color: "text-purple-500"
    },
    {
      icon: <CheckSquare className="h-8 w-8" />,
      title: "任务管理",
      description: "创建、跟踪和管理项目任务，提高工作效率",
      link: "/tasks",
      color: "text-orange-500"
    }
  ]

  const models = [
    { name: "GPT-4", provider: "OpenAI", specialty: "通用对话" },
    { name: "Claude 3.5", provider: "Anthropic", specialty: "代码分析" },
    { name: "Gemini Pro", provider: "Google", specialty: "多模态处理" },
    { name: "Llama 3", provider: "Meta", specialty: "本地部署" }
  ]

  return (
    <div className="space-y-8">
      {/* 欢迎区域 */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-2">
          <Brain className="h-12 w-12 text-primary" />
          <h1 className="text-4xl font-bold text-foreground">
            CloudAI Assistant
          </h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          功能强大的云端AI助手平台，集成多个AI模型，为您提供智能对话、代码分析、文件处理和任务管理服务
        </p>
      </div>

      {/* 快速开始 */}
      <div className="bg-card rounded-lg p-6 border">
        <div className="flex items-center space-x-2 mb-4">
          <Zap className="h-5 w-5 text-primary" />
          <h2 className="text-xl font-semibold">快速开始</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {features.map((feature, index) => (
            <Link
              key={index}
              to={feature.link}
              className="group p-4 rounded-lg border hover:border-primary/50 transition-colors"
            >
              <div className={`${feature.color} mb-3 group-hover:scale-110 transition-transform`}>
                {feature.icon}
              </div>
              <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                {feature.title}
              </h3>
              <p className="text-sm text-muted-foreground">
                {feature.description}
              </p>
            </Link>
          ))}
        </div>
      </div>

      {/* AI模型展示 */}
      <div className="bg-card rounded-lg p-6 border">
        <div className="flex items-center space-x-2 mb-4">
          <Sparkles className="h-5 w-5 text-primary" />
          <h2 className="text-xl font-semibold">支持的AI模型</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {models.map((model, index) => (
            <div key={index} className="p-4 rounded-lg bg-muted/50">
              <h3 className="font-semibold text-foreground">{model.name}</h3>
              <p className="text-sm text-muted-foreground mb-1">{model.provider}</p>
              <p className="text-xs text-primary">{model.specialty}</p>
            </div>
          ))}
        </div>
      </div>

      {/* 功能特性 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-card rounded-lg p-6 border">
          <h3 className="text-lg font-semibold mb-3">🚀 高性能</h3>
          <p className="text-muted-foreground">
            基于云原生架构，支持高并发访问，响应速度快，稳定可靠
          </p>
        </div>
        <div className="bg-card rounded-lg p-6 border">
          <h3 className="text-lg font-semibold mb-3">🔒 安全可靠</h3>
          <p className="text-muted-foreground">
            企业级安全保障，数据加密传输，用户隐私保护，访问权限控制
          </p>
        </div>
        <div className="bg-card rounded-lg p-6 border">
          <h3 className="text-lg font-semibold mb-3">🎯 智能路由</h3>
          <p className="text-muted-foreground">
            智能选择最适合的AI模型，自动负载均衡，成本优化，故障转移
          </p>
        </div>
      </div>

      {/* 开始使用 */}
      <div className="text-center bg-primary/5 rounded-lg p-8 border border-primary/20">
        <h2 className="text-2xl font-bold mb-4">准备开始了吗？</h2>
        <p className="text-muted-foreground mb-6">
          立即开始与AI助手对话，体验智能化的工作流程
        </p>
        <Link
          to="/chat"
          className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
        >
          <MessageSquare className="h-5 w-5 mr-2" />
          开始对话
        </Link>
      </div>
    </div>
  )
}

export default HomePage
