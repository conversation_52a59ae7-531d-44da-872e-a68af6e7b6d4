"""
文件模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Boolean, BigInteger
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.session import Base


class File(Base):
    """文件模型"""
    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)
    
    # 用户关联
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 文件基本信息
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger, nullable=False)  # 文件大小（字节）
    mime_type = Column(String(100), nullable=False)
    file_extension = Column(String(10), nullable=False)
    
    # 文件内容
    content = Column(Text, nullable=True)  # 文本文件内容
    content_hash = Column(String(64), nullable=False)  # 文件内容哈希
    
    # 文件分类
    category = Column(String(50), default="document")  # document, image, code, data等
    tags = Column(JSON, default=list)  # 标签列表
    
    # 文件状态
    is_public = Column(Boolean, default=False)
    is_processed = Column(Boolean, default=False)  # 是否已处理（如提取文本等）
    is_deleted = Column(Boolean, default=False)
    
    # 处理信息
    processing_status = Column(String(20), default="pending")  # pending, processing, completed, failed
    processing_error = Column(Text, nullable=True)
    
    # 文件元数据
    metadata = Column(JSON, default=dict)  # 额外的文件元数据
    
    # 访问统计
    download_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_accessed = Column(DateTime(timezone=True), nullable=True)
    
    # 关系
    user = relationship("User", back_populates="files")

    def __repr__(self):
        return f"<File(id={self.id}, filename='{self.filename}', user_id={self.user_id})>"
