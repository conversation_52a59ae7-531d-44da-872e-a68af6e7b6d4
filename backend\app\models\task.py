"""
任务模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Boolean, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.db.session import Base


class TaskStatus(str, enum.Enum):
    """任务状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class TaskPriority(str, enum.Enum):
    """任务优先级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class Task(Base):
    """任务模型"""
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True)
    
    # 用户关联
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 任务基本信息
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # 任务层级关系
    parent_task_id = Column(Integer, ForeignKey("tasks.id"), nullable=True)
    order_index = Column(Integer, default=0)  # 排序索引
    
    # 任务状态
    status = Column(Enum(TaskStatus), default=TaskStatus.NOT_STARTED)
    priority = Column(Enum(TaskPriority), default=TaskPriority.MEDIUM)
    progress = Column(Integer, default=0)  # 进度百分比 0-100
    
    # 时间管理
    estimated_hours = Column(Integer, nullable=True)  # 预估工时（小时）
    actual_hours = Column(Integer, default=0)  # 实际工时（小时）
    due_date = Column(DateTime(timezone=True), nullable=True)  # 截止日期
    started_at = Column(DateTime(timezone=True), nullable=True)  # 开始时间
    completed_at = Column(DateTime(timezone=True), nullable=True)  # 完成时间
    
    # 任务分类
    category = Column(String(50), nullable=True)  # 任务分类
    tags = Column(JSON, default=list)  # 标签列表
    
    # 任务元数据
    metadata = Column(JSON, default=dict)  # 额外的任务数据
    attachments = Column(JSON, default=list)  # 附件列表
    
    # 协作信息
    assignees = Column(JSON, default=list)  # 分配给的用户ID列表
    watchers = Column(JSON, default=list)  # 关注者用户ID列表
    
    # 任务设置
    is_recurring = Column(Boolean, default=False)  # 是否重复任务
    recurrence_pattern = Column(String(100), nullable=True)  # 重复模式
    is_template = Column(Boolean, default=False)  # 是否为模板
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="tasks")
    parent_task = relationship("Task", remote_side=[id], backref="subtasks")

    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status}')>"
