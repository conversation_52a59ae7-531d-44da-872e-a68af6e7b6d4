@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 CloudAI Assistant 快速测试启动器
echo ========================================
echo.

echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.11+
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 检查并安装必要依赖...
pip install fastapi uvicorn python-dotenv >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 依赖安装可能有问题，但继续尝试启动...
) else (
    echo ✅ 依赖安装完成
)
echo.

echo 🔧 检查配置文件...
if not exist ".env" (
    echo 📝 创建配置文件...
    python simple_setup.py
    echo.
    echo ⚠️ 请编辑 .env 文件，填入您的AI API密钥后重新运行
    echo 💡 或者直接按回车使用测试模式
    pause
)

echo 🚀 启动服务器...
echo 📡 服务地址: http://localhost:8000
echo 💬 测试页面: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo.
echo 💡 提示: 按 Ctrl+C 停止服务器
echo.

if exist "simple_backend.py" (
    python simple_backend.py
) else (
    python test_server.py
)

echo.
echo 👋 服务器已停止
pause
