#!/usr/bin/env python3
"""
CloudAI Assistant 启动脚本
"""
import os
import sys
import subprocess
import time
import signal
from pathlib import Path
from threading import Thread


class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
    
    def start_service(self, name: str, command: str, cwd: str = None):
        """启动服务"""
        try:
            print(f"🚀 启动 {name}...")
            process = subprocess.Popen(
                command.split(),
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            self.processes[name] = process
            print(f"✅ {name} 已启动 (PID: {process.pid})")
            return True
        except Exception as e:
            print(f"❌ {name} 启动失败: {e}")
            return False
    
    def stop_all_services(self):
        """停止所有服务"""
        print("\n🛑 正在停止所有服务...")
        self.running = False
        
        for name, process in self.processes.items():
            try:
                print(f"⏹️ 停止 {name}...")
                process.terminate()
                process.wait(timeout=10)
                print(f"✅ {name} 已停止")
            except subprocess.TimeoutExpired:
                print(f"⚠️ 强制终止 {name}...")
                process.kill()
            except Exception as e:
                print(f"❌ 停止 {name} 时出错: {e}")
    
    def monitor_services(self):
        """监控服务状态"""
        while self.running:
            time.sleep(5)
            for name, process in list(self.processes.items()):
                if process.poll() is not None:
                    print(f"⚠️ {name} 意外退出 (返回码: {process.returncode})")
                    # 可以在这里添加重启逻辑
            
            if not self.running:
                break


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env 文件不存在")
        print("请先运行 'python scripts/setup.py' 进行初始化")
        return False
    
    # 检查关键环境变量
    required_vars = [
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY", 
        "GOOGLE_API_KEY"
    ]
    
    missing_vars = []
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=your-" in content or f"{var}=" not in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️ 以下环境变量需要配置: {', '.join(missing_vars)}")
        print("请编辑 .env 文件，填入正确的API密钥")
        
        choice = input("是否继续启动？(y/N): ").lower()
        if choice != 'y':
            return False
    
    print("✅ 环境配置检查完成")
    return True


def start_infrastructure():
    """启动基础设施服务"""
    print("🏗️ 启动基础设施服务...")
    
    try:
        result = subprocess.run(
            ["docker-compose", "up", "-d", "postgres", "redis", "minio"],
            check=True,
            capture_output=True,
            text=True
        )
        print("✅ 基础设施服务已启动")
        
        # 等待服务就绪
        print("⏳ 等待服务就绪...")
        time.sleep(10)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 基础设施服务启动失败: {e.stderr}")
        return False


def main():
    """主函数"""
    print("🚀 CloudAI Assistant 启动器")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 启动基础设施
    if not start_infrastructure():
        sys.exit(1)
    
    # 创建服务管理器
    manager = ServiceManager()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print(f"\n📡 收到信号 {signum}")
        manager.stop_all_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动后端服务
        backend_success = manager.start_service(
            "Backend API",
            "python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000",
            "backend"
        )
        
        if backend_success:
            # 等待后端启动
            print("⏳ 等待后端服务就绪...")
            time.sleep(5)
            
            # 启动前端服务
            frontend_success = manager.start_service(
                "Frontend",
                "npm run dev",
                "frontend"
            )
            
            if frontend_success:
                print("\n🎉 所有服务已启动！")
                print("\n📋 服务地址:")
                print("🌐 前端应用: http://localhost:3000")
                print("🔧 后端API: http://localhost:8000")
                print("📚 API文档: http://localhost:8000/docs")
                print("🗄️ MinIO控制台: http://localhost:9001")
                print("\n按 Ctrl+C 停止所有服务")
                
                # 启动监控线程
                monitor_thread = Thread(target=manager.monitor_services)
                monitor_thread.daemon = True
                monitor_thread.start()
                
                # 保持主线程运行
                while manager.running:
                    time.sleep(1)
            else:
                print("❌ 前端服务启动失败")
                manager.stop_all_services()
                sys.exit(1)
        else:
            print("❌ 后端服务启动失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 用户中断")
        manager.stop_all_services()
    except Exception as e:
        print(f"\n❌ 启动过程中出错: {e}")
        manager.stop_all_services()
        sys.exit(1)


if __name__ == "__main__":
    main()
