import React from 'react'
import { Upload, FileText, Image, Code, Download, Trash2 } from 'lucide-react'

const FilesPage: React.FC = () => {
  const mockFiles = [
    {
      id: 1,
      name: 'project-readme.md',
      type: 'markdown',
      size: '2.3 KB',
      uploadDate: '2024-01-15',
      icon: FileText
    },
    {
      id: 2,
      name: 'main.py',
      type: 'python',
      size: '5.7 KB',
      uploadDate: '2024-01-14',
      icon: Code
    },
    {
      id: 3,
      name: 'screenshot.png',
      type: 'image',
      size: '1.2 MB',
      uploadDate: '2024-01-13',
      icon: Image
    }
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-foreground">文件管理</h1>
        <button className="flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
          <Upload className="h-4 w-4 mr-2" />
          上传文件
        </button>
      </div>

      {/* 上传区域 */}
      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-primary/50 transition-colors">
        <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium text-foreground mb-2">拖拽文件到此处上传</h3>
        <p className="text-muted-foreground mb-4">
          支持 .txt, .md, .py, .js, .json, .yaml 等文件格式
        </p>
        <button className="px-4 py-2 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors">
          选择文件
        </button>
      </div>

      {/* 文件列表 */}
      <div className="bg-card rounded-lg border">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold text-foreground">我的文件</h2>
        </div>
        <div className="divide-y">
          {mockFiles.map((file) => {
            const Icon = file.icon
            return (
              <div key={file.id} className="p-4 flex items-center justify-between hover:bg-muted/50 transition-colors">
                <div className="flex items-center space-x-3">
                  <Icon className="h-8 w-8 text-primary" />
                  <div>
                    <h3 className="font-medium text-foreground">{file.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {file.size} • 上传于 {file.uploadDate}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-colors">
                    <Download className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-muted-foreground hover:text-destructive hover:bg-muted rounded-lg transition-colors">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-card p-4 rounded-lg border">
          <h3 className="text-sm font-medium text-muted-foreground">总文件数</h3>
          <p className="text-2xl font-bold text-foreground">12</p>
        </div>
        <div className="bg-card p-4 rounded-lg border">
          <h3 className="text-sm font-medium text-muted-foreground">总大小</h3>
          <p className="text-2xl font-bold text-foreground">45.2 MB</p>
        </div>
        <div className="bg-card p-4 rounded-lg border">
          <h3 className="text-sm font-medium text-muted-foreground">本月上传</h3>
          <p className="text-2xl font-bold text-foreground">8</p>
        </div>
      </div>
    </div>
  )
}

export default FilesPage
