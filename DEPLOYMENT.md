# 🚀 CloudAI Assistant 部署指南

## 📋 部署前准备

### 系统要求
- **操作系统**: Linux/macOS/Windows
- **Python**: 3.11+
- **Node.js**: 18+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 最少 4GB，推荐 8GB+
- **存储**: 最少 20GB 可用空间

### API密钥准备
在部署前，请准备以下API密钥：
- **OpenAI API Key**: https://platform.openai.com/api-keys
- **Anthropic API Key**: https://console.anthropic.com/
- **Google AI API Key**: https://makersuite.google.com/app/apikey

## 🛠️ 本地开发部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd cloudai-assistant
```

### 2. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件，填入您的API密钥
nano .env
```

### 3. 自动化设置
```bash
# 运行自动化设置脚本
python scripts/setup.py
```

### 4. 启动服务
```bash
# 启动所有服务
python scripts/start.py
```

### 5. 访问应用
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **MinIO控制台**: http://localhost:9001

## 🐳 Docker 部署

### 1. 使用 Docker Compose
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 2. 单独构建镜像
```bash
# 构建后端镜像
docker build -t cloudai-backend ./backend

# 构建前端镜像
docker build -t cloudai-frontend ./frontend
```

## ☁️ 云端生产部署

### AWS 部署

#### 1. 使用 ECS + RDS
```bash
# 创建 ECS 集群
aws ecs create-cluster --cluster-name cloudai-cluster

# 创建 RDS 实例
aws rds create-db-instance \
  --db-instance-identifier cloudai-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username postgres \
  --master-user-password your-password \
  --allocated-storage 20
```

#### 2. 使用 EKS (Kubernetes)
```bash
# 创建 EKS 集群
eksctl create cluster --name cloudai-cluster --region us-west-2

# 部署应用
kubectl apply -f infrastructure/k8s/
```

### 阿里云部署

#### 1. 使用容器服务 ACK
```bash
# 创建 ACK 集群
aliyun cs CreateCluster \
  --name cloudai-cluster \
  --cluster-type ManagedKubernetes \
  --region cn-hangzhou
```

#### 2. 使用 ECS + RDS
```bash
# 创建 ECS 实例
aliyun ecs CreateInstance \
  --ImageId ubuntu_20_04_x64_20G_alibase_20210420.vhd \
  --InstanceType ecs.t6-c1m1.large \
  --SecurityGroupId sg-xxx
```

### 腾讯云部署

#### 1. 使用 TKE (容器服务)
```bash
# 创建 TKE 集群
tccli tke CreateCluster \
  --ClusterName cloudai-cluster \
  --ClusterVersion 1.20.6
```

## 🔧 环境变量配置

### 必需配置
```env
# AI模型API密钥
OPENAI_API_KEY=sk-xxx
ANTHROPIC_API_KEY=sk-ant-xxx
GOOGLE_API_KEY=AIzaSyxxx

# 数据库配置
POSTGRES_SERVER=your-db-host
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-password
POSTGRES_DB=cloudai_assistant

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password

# 安全配置
SECRET_KEY=your-secret-key
```

### 可选配置
```env
# 文件存储
MINIO_ENDPOINT=your-minio-endpoint
MINIO_ACCESS_KEY=your-access-key
MINIO_SECRET_KEY=your-secret-key

# 邮件服务
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# 监控配置
ENABLE_METRICS=true
SENTRY_DSN=your-sentry-dsn
```

## 📊 监控和日志

### Prometheus + Grafana
```bash
# 启动监控服务
docker-compose up -d prometheus grafana

# 访问 Grafana
open http://localhost:3001
# 默认账户: admin/admin123
```

### 日志收集
```bash
# 查看应用日志
docker-compose logs -f backend frontend

# 使用 ELK Stack
docker-compose -f docker-compose.monitoring.yml up -d
```

## 🔒 安全配置

### SSL/TLS 证书
```bash
# 使用 Let's Encrypt
certbot --nginx -d your-domain.com

# 或使用 Cloudflare
# 在 Cloudflare 控制台配置 SSL
```

### 防火墙配置
```bash
# Ubuntu/Debian
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp
ufw enable

# CentOS/RHEL
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --reload
```

## 🚀 性能优化

### 数据库优化
```sql
-- PostgreSQL 配置优化
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

### Redis 优化
```conf
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### Nginx 优化
```nginx
# nginx.conf
worker_processes auto;
worker_connections 1024;

gzip on;
gzip_types text/plain text/css application/json application/javascript;

client_max_body_size 100M;
```

## 🔄 备份和恢复

### 数据库备份
```bash
# 创建备份
pg_dump -h localhost -U postgres cloudai_assistant > backup.sql

# 恢复备份
psql -h localhost -U postgres cloudai_assistant < backup.sql
```

### 文件备份
```bash
# 备份上传文件
tar -czf uploads-backup.tar.gz uploads/

# 备份 MinIO 数据
mc mirror minio/cloudai-files ./backup/files/
```

## 📈 扩容指南

### 水平扩容
```bash
# 增加后端实例
docker-compose up -d --scale backend=3

# 使用 Kubernetes HPA
kubectl autoscale deployment backend --cpu-percent=70 --min=2 --max=10
```

### 垂直扩容
```yaml
# docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
```

## 🆘 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库状态
docker-compose ps postgres
docker-compose logs postgres

# 测试连接
psql -h localhost -U postgres -d cloudai_assistant
```

#### 2. AI API 调用失败
```bash
# 检查 API 密钥
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models

# 查看后端日志
docker-compose logs backend | grep -i error
```

#### 3. 前端无法访问
```bash
# 检查前端服务
docker-compose ps frontend
curl http://localhost:3000

# 检查代理配置
nginx -t
```

### 性能问题诊断
```bash
# 查看资源使用
docker stats

# 查看数据库性能
SELECT * FROM pg_stat_activity;

# 查看 Redis 性能
redis-cli info stats
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看项目文档: [GitHub Wiki]
2. 提交 Issue: [GitHub Issues]
3. 联系技术支持: <EMAIL>

---

🎉 **恭喜！您已成功部署 CloudAI Assistant！**
