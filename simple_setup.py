#!/usr/bin/env python3
"""
CloudAI Assistant 简化设置脚本
专为Windows环境优化
"""
import os
import shutil
from pathlib import Path


def create_env_file():
    """创建环境变量文件"""
    print("⚙️ 创建环境配置文件...")
    
    env_content = """# CloudAI Assistant 环境配置
# 基础配置
PROJECT_NAME=CloudAI Assistant
VERSION=1.0.0
DEBUG=true
SECRET_KEY=test-secret-key-for-development

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 数据库配置 (SQLite for testing)
DATABASE_URL=sqlite:///./test.db

# Redis配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379

# AI模型API密钥 (请填入您的真实密钥)
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
GOOGLE_API_KEY=your-google-api-key-here

# 模型配置
DEFAULT_MODEL=gpt-4
MAX_TOKENS=4096
TEMPERATURE=0.7

# 文件存储配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600

# 日志配置
LOG_LEVEL=INFO
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ 环境配置文件已创建")
    print("⚠️ 请编辑 .env 文件，填入您的AI API密钥")


def create_simple_backend():
    """创建简化的后端启动文件"""
    print("🐍 创建简化后端...")
    
    backend_content = """#!/usr/bin/env python3
\"\"\"
CloudAI Assistant 简化后端服务
\"\"\"
import os
import sqlite3
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ python-dotenv 未安装，使用默认配置")

# 创建FastAPI应用
app = FastAPI(title="CloudAI Assistant", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class ChatMessage(BaseModel):
    content: str

class ChatResponse(BaseModel):
    content: str
    model: str
    timestamp: str

# 初始化SQLite数据库
def init_db():
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS conversations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_message TEXT,
            ai_response TEXT,
            timestamp TEXT
        )
    ''')
    conn.commit()
    conn.close()

# 模拟AI响应
def generate_response(message: str) -> str:
    responses = {
        "你好": "您好！我是CloudAI Assistant，很高兴为您服务！",
        "介绍": "我是一个功能强大的AI助手，可以帮您进行对话、代码分析、文件处理和任务管理。",
        "功能": "我的主要功能包括：\\n1. 智能对话\\n2. 代码分析\\n3. 文件管理\\n4. 任务规划",
        "帮助": "您可以与我自由对话，询问各种问题。这是测试版本，完整版本会集成真实的AI模型。"
    }
    
    for keyword, response in responses.items():
        if keyword in message:
            return response
    
    return f"我收到了您的消息：「{message}」\\n\\n这是一个测试响应。在完整版本中，这里会调用真实的AI模型来生成智能回复。"

# API路由
@app.get("/")
async def root():
    return {"message": "CloudAI Assistant API is running!", "version": "1.0.0"}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/api/v1/chat", response_model=ChatResponse)
async def chat(message: ChatMessage):
    try:
        # 生成响应
        ai_response = generate_response(message.content)
        
        # 保存到数据库
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO conversations (user_message, ai_response, timestamp) VALUES (?, ?, ?)",
            (message.content, ai_response, datetime.now().isoformat())
        )
        conn.commit()
        conn.close()
        
        return ChatResponse(
            content=ai_response,
            model="test-model",
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/conversations")
async def get_conversations():
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM conversations ORDER BY id DESC LIMIT 10")
    conversations = cursor.fetchall()
    conn.close()
    
    return {"conversations": [
        {"id": c[0], "user_message": c[1], "ai_response": c[2], "timestamp": c[3]}
        for c in conversations
    ]}

if __name__ == "__main__":
    print("🚀 启动CloudAI Assistant后端服务...")
    init_db()
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
"""
    
    with open('simple_backend.py', 'w', encoding='utf-8') as f:
        f.write(backend_content)
    
    print("✅ 简化后端文件已创建")


def main():
    """主函数"""
    print("🚀 CloudAI Assistant 简化设置")
    print("=" * 50)
    
    # 创建环境文件
    create_env_file()
    
    # 创建简化后端
    create_simple_backend()
    
    # 创建uploads目录
    os.makedirs('uploads', exist_ok=True)
    print("✅ 上传目录已创建")
    
    print("\n🎉 设置完成！")
    print("\n📋 下一步:")
    print("1. 编辑 .env 文件，填入您的AI API密钥")
    print("2. 安装依赖: pip install fastapi uvicorn python-dotenv")
    print("3. 启动服务: python simple_backend.py")
    print("4. 访问: http://localhost:8000")
    print("5. API文档: http://localhost:8000/docs")


if __name__ == "__main__":
    main()
