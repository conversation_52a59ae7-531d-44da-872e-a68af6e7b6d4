import React, { useState } from 'react'
import { Plus, CheckSquare, Square, Clock, X } from 'lucide-react'

const TasksPage: React.FC = () => {
  const [tasks, setTasks] = useState([
    {
      id: 1,
      title: '完成AI助手项目架构设计',
      description: '设计整体系统架构，包括前端界面、后端API、AI模型集成等',
      status: 'completed',
      priority: 'high',
      dueDate: '2024-01-15',
      createdAt: '2024-01-10'
    },
    {
      id: 2,
      title: '实现用户认证系统',
      description: '开发用户注册、登录、权限管理等功能',
      status: 'in_progress',
      priority: 'medium',
      dueDate: '2024-01-20',
      createdAt: '2024-01-12'
    },
    {
      id: 3,
      title: '集成多个AI模型',
      description: '集成OpenAI、Anthropic、Google等多个AI模型',
      status: 'not_started',
      priority: 'high',
      dueDate: '2024-01-25',
      createdAt: '2024-01-14'
    }
  ])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckSquare className="h-5 w-5 text-green-500" />
      case 'in_progress':
        return <Clock className="h-5 w-5 text-blue-500" />
      case 'not_started':
        return <Square className="h-5 w-5 text-muted-foreground" />
      default:
        return <Square className="h-5 w-5 text-muted-foreground" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成'
      case 'in_progress':
        return '进行中'
      case 'not_started':
        return '未开始'
      default:
        return '未知'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-500'
      case 'medium':
        return 'text-yellow-500'
      case 'low':
        return 'text-green-500'
      default:
        return 'text-muted-foreground'
    }
  }

  const toggleTaskStatus = (taskId: number) => {
    setTasks(tasks.map(task => {
      if (task.id === taskId) {
        const newStatus = task.status === 'completed' ? 'not_started' : 
                         task.status === 'not_started' ? 'in_progress' : 'completed'
        return { ...task, status: newStatus }
      }
      return task
    }))
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-foreground">任务管理</h1>
        <button className="flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
          <Plus className="h-4 w-4 mr-2" />
          新建任务
        </button>
      </div>

      {/* 任务统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-card p-4 rounded-lg border">
          <h3 className="text-sm font-medium text-muted-foreground">总任务</h3>
          <p className="text-2xl font-bold text-foreground">{tasks.length}</p>
        </div>
        <div className="bg-card p-4 rounded-lg border">
          <h3 className="text-sm font-medium text-muted-foreground">已完成</h3>
          <p className="text-2xl font-bold text-green-500">
            {tasks.filter(t => t.status === 'completed').length}
          </p>
        </div>
        <div className="bg-card p-4 rounded-lg border">
          <h3 className="text-sm font-medium text-muted-foreground">进行中</h3>
          <p className="text-2xl font-bold text-blue-500">
            {tasks.filter(t => t.status === 'in_progress').length}
          </p>
        </div>
        <div className="bg-card p-4 rounded-lg border">
          <h3 className="text-sm font-medium text-muted-foreground">未开始</h3>
          <p className="text-2xl font-bold text-muted-foreground">
            {tasks.filter(t => t.status === 'not_started').length}
          </p>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="bg-card rounded-lg border">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold text-foreground">任务列表</h2>
        </div>
        <div className="divide-y">
          {tasks.map((task) => (
            <div key={task.id} className="p-4 hover:bg-muted/50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <button
                    onClick={() => toggleTaskStatus(task.id)}
                    className="mt-1 hover:scale-110 transition-transform"
                  >
                    {getStatusIcon(task.status)}
                  </button>
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-medium ${task.status === 'completed' ? 'line-through text-muted-foreground' : 'text-foreground'}`}>
                      {task.title}
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {task.description}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                      <span className={`font-medium ${getPriorityColor(task.priority)}`}>
                        {task.priority === 'high' ? '高优先级' : 
                         task.priority === 'medium' ? '中优先级' : '低优先级'}
                      </span>
                      <span>截止: {task.dueDate}</span>
                      <span>状态: {getStatusText(task.status)}</span>
                    </div>
                  </div>
                </div>
                <button className="p-1 text-muted-foreground hover:text-destructive hover:bg-muted rounded transition-colors">
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default TasksPage
