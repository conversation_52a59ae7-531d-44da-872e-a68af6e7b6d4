{"name": "<PERSON><PERSON>-assistant-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "CloudAI Assistant 前端应用", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "zustand": "^4.4.7", "socket.io-client": "^4.7.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-syntax-highlighter": "^15.5.10", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}