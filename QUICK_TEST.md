# 🚀 CloudAI Assistant 快速测试指南

## 方案1: 最小化本地测试 (5分钟启动)

### 前置要求
- Python 3.11+
- Node.js 18+
- 一个AI API密钥 (OpenAI/Anthropic/Google任选其一)

### 步骤1: 环境配置
```bash
# 1. 复制环境变量
copy .env.example .env

# 2. 编辑 .env 文件，至少配置一个AI API密钥
# 例如：OPENAI_API_KEY=sk-your-key-here
```

### 步骤2: 启动后端 (简化版)
```bash
cd backend

# 安装依赖
pip install fastapi uvicorn python-dotenv openai anthropic google-generativeai

# 创建简化启动文件
echo 'from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import os
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(title="CloudAI Assistant API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"message": "CloudAI Assistant API is running!"}

@app.get("/health")
def health():
    return {"status": "healthy"}

@app.post("/api/v1/chat")
async def chat(message: dict):
    # 简化的聊天接口
    user_message = message.get("content", "")
    
    # 这里可以集成真实的AI API
    response = f"AI回复: 我收到了您的消息 \"{user_message}\"。这是一个测试响应。"
    
    return {
        "content": response,
        "model": "test-model",
        "tokens_used": len(user_message) + len(response)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)' > test_main.py

# 启动后端
python test_main.py
```

### 步骤3: 启动前端
```bash
# 新开一个终端
cd frontend

# 安装依赖
npm install

# 启动前端
npm run dev
```

### 步骤4: 测试访问
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

---

## 方案2: Docker快速测试 (推荐)

### 前置要求
- Docker Desktop
- 一个AI API密钥

### 步骤1: 配置环境
```bash
# 复制并编辑环境变量
copy .env.example .env
# 编辑 .env 文件，填入API密钥
```

### 步骤2: 启动基础服务
```bash
# 只启动必要的服务进行测试
docker-compose up -d postgres redis

# 等待服务启动 (约30秒)
timeout 30
```

### 步骤3: 启动应用
```bash
# 启动后端和前端
docker-compose up backend frontend

# 或者后台运行
docker-compose up -d backend frontend
```

### 步骤4: 验证服务
```bash
# 检查服务状态
docker-compose ps

# 测试后端API
curl http://localhost:8000/health

# 测试前端
curl http://localhost:3000
```

---

## 方案3: 完整功能测试

### 启动所有服务
```bash
# 启动完整的服务栈
docker-compose up -d

# 查看所有服务状态
docker-compose ps
```

### 访问各个服务
- 🌐 前端应用: http://localhost:3000
- 🔧 后端API: http://localhost:8000/docs
- 🗄️ MinIO控制台: http://localhost:9001 (minioadmin/minioadmin123)
- 📊 Grafana监控: http://localhost:3001 (admin/admin123)
- 📈 Prometheus: http://localhost:9090

---

## 🧪 功能测试清单

### 基础功能测试
- [ ] 前端页面正常加载
- [ ] 后端API健康检查通过
- [ ] 用户注册/登录功能
- [ ] AI对话功能
- [ ] 文件上传功能
- [ ] 任务管理功能

### AI功能测试
```bash
# 测试AI API调用
curl -X POST http://localhost:8000/api/v1/chat \
  -H "Content-Type: application/json" \
  -d '{"content": "你好，请介绍一下自己"}'
```

### 数据库测试
```bash
# 连接PostgreSQL测试
docker exec -it cloudai-postgres psql -U postgres -d cloudai_assistant -c "\dt"
```

### 文件存储测试
```bash
# 测试MinIO连接
curl http://localhost:9000/minio/health/live
```

---

## 🐛 常见问题解决

### 问题1: 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 修改端口 (在docker-compose.yml中)
# 将 "8000:8000" 改为 "8001:8000"
```

### 问题2: Docker服务启动失败
```bash
# 查看详细日志
docker-compose logs backend
docker-compose logs frontend

# 重新构建镜像
docker-compose build --no-cache
```

### 问题3: API密钥配置问题
```bash
# 检查环境变量是否正确加载
docker-compose exec backend env | grep API_KEY
```

### 问题4: 前端无法连接后端
```bash
# 检查网络连接
curl http://localhost:8000/health

# 检查CORS配置
# 确保后端允许前端域名访问
```

---

## 📊 性能测试

### 简单压力测试
```bash
# 安装测试工具
pip install locust

# 创建测试脚本
echo 'from locust import HttpUser, task, between

class WebsiteUser(HttpUser):
    wait_time = between(1, 3)
    
    @task
    def test_health(self):
        self.client.get("/health")
    
    @task
    def test_chat(self):
        self.client.post("/api/v1/chat", json={"content": "测试消息"})' > locustfile.py

# 运行压力测试
locust -f locustfile.py --host=http://localhost:8000
# 访问 http://localhost:8089 查看测试界面
```

---

## 🔧 开发调试

### 后端调试
```bash
# 进入后端容器
docker-compose exec backend bash

# 查看Python日志
docker-compose logs -f backend

# 手动运行后端 (开发模式)
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 前端调试
```bash
# 进入前端容器
docker-compose exec frontend sh

# 查看前端日志
docker-compose logs -f frontend

# 手动运行前端 (开发模式)
cd frontend
npm run dev
```

### 数据库调试
```bash
# 连接数据库
docker-compose exec postgres psql -U postgres -d cloudai_assistant

# 查看表结构
\dt
\d users
```

---

## 🎯 测试建议

1. **从简单开始**: 先用方案1测试基本功能
2. **逐步完善**: 确认基础功能后再测试完整功能
3. **检查日志**: 遇到问题时优先查看日志
4. **分步测试**: 一个功能一个功能地测试
5. **保存配置**: 测试成功后保存好配置文件

选择适合您的测试方案开始吧！如果遇到任何问题，我会帮您解决。
