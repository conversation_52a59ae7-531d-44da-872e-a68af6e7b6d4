"""
AI模型路由器 - 智能选择和调度不同的AI模型
"""
from typing import Dict, List, Optional, Any, AsyncGenerator
from enum import Enum
import asyncio
import logging
from dataclasses import dataclass

from ai_models.adapters.openai_adapter import OpenAIAdapter
from ai_models.adapters.anthropic_adapter import AnthropicAdapter
from ai_models.adapters.gemini_adapter import GeminiAdapter
from ai_models.adapters.local_adapter import LocalModelAdapter
from ai_models.configs.model_config import ModelConfig, TaskType


logger = logging.getLogger(__name__)


class ModelProvider(str, Enum):
    """模型提供商枚举"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    LOCAL = "local"


@dataclass
class ModelResponse:
    """模型响应数据类"""
    content: str
    model: str
    provider: ModelProvider
    tokens_used: int
    cost: float
    latency: float
    metadata: Dict[str, Any]


class ModelRouter:
    """AI模型路由器"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.adapters: Dict[ModelProvider, Any] = {}
        self.model_health: Dict[str, bool] = {}
        self._initialize_adapters()
    
    def _initialize_adapters(self):
        """初始化模型适配器"""
        try:
            # OpenAI适配器
            if self.config.openai_api_key:
                self.adapters[ModelProvider.OPENAI] = OpenAIAdapter(
                    api_key=self.config.openai_api_key,
                    base_url=self.config.openai_base_url
                )
                logger.info("✅ OpenAI适配器初始化成功")
            
            # Anthropic适配器
            if self.config.anthropic_api_key:
                self.adapters[ModelProvider.ANTHROPIC] = AnthropicAdapter(
                    api_key=self.config.anthropic_api_key
                )
                logger.info("✅ Anthropic适配器初始化成功")
            
            # Google Gemini适配器
            if self.config.google_api_key:
                self.adapters[ModelProvider.GOOGLE] = GeminiAdapter(
                    api_key=self.config.google_api_key
                )
                logger.info("✅ Google Gemini适配器初始化成功")
            
            # 本地模型适配器
            if self.config.enable_local_models:
                self.adapters[ModelProvider.LOCAL] = LocalModelAdapter(
                    model_path=self.config.local_model_path
                )
                logger.info("✅ 本地模型适配器初始化成功")
                
        except Exception as e:
            logger.error(f"❌ 模型适配器初始化失败: {e}")
    
    def select_best_model(
        self, 
        task_type: TaskType, 
        user_preference: Optional[str] = None,
        cost_priority: bool = False
    ) -> tuple[ModelProvider, str]:
        """
        根据任务类型和用户偏好选择最佳模型
        
        Args:
            task_type: 任务类型
            user_preference: 用户偏好模型
            cost_priority: 是否优先考虑成本
            
        Returns:
            (provider, model_name) 元组
        """
        # 任务类型到模型的映射
        task_model_mapping = {
            TaskType.CONVERSATION: [
                (ModelProvider.OPENAI, "gpt-4o"),
                (ModelProvider.ANTHROPIC, "claude-3-5-sonnet-20241022"),
                (ModelProvider.GOOGLE, "gemini-pro"),
            ],
            TaskType.CODE_ANALYSIS: [
                (ModelProvider.ANTHROPIC, "claude-3-5-sonnet-20241022"),
                (ModelProvider.OPENAI, "gpt-4o"),
                (ModelProvider.LOCAL, "codellama"),
            ],
            TaskType.CODE_GENERATION: [
                (ModelProvider.ANTHROPIC, "claude-3-5-sonnet-20241022"),
                (ModelProvider.OPENAI, "gpt-4o"),
                (ModelProvider.LOCAL, "codellama"),
            ],
            TaskType.TEXT_PROCESSING: [
                (ModelProvider.OPENAI, "gpt-4o-mini"),
                (ModelProvider.GOOGLE, "gemini-pro"),
                (ModelProvider.LOCAL, "llama3"),
            ],
            TaskType.MULTIMODAL: [
                (ModelProvider.GOOGLE, "gemini-pro-vision"),
                (ModelProvider.OPENAI, "gpt-4o"),
            ],
        }
        
        # 获取任务对应的模型列表
        candidate_models = task_model_mapping.get(task_type, [])
        
        # 如果用户有偏好，优先使用用户偏好
        if user_preference:
            for provider, model in candidate_models:
                if model == user_preference and provider in self.adapters:
                    if self._is_model_healthy(provider, model):
                        return provider, model
        
        # 根据成本优先级排序
        if cost_priority:
            candidate_models = sorted(
                candidate_models, 
                key=lambda x: self._get_model_cost(x[0], x[1])
            )
        
        # 选择第一个可用的模型
        for provider, model in candidate_models:
            if provider in self.adapters and self._is_model_healthy(provider, model):
                return provider, model
        
        # 如果没有找到合适的模型，返回默认模型
        if ModelProvider.OPENAI in self.adapters:
            return ModelProvider.OPENAI, "gpt-4o-mini"
        
        raise RuntimeError("没有可用的AI模型")
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        task_type: TaskType = TaskType.CONVERSATION,
        user_preference: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> ModelResponse:
        """
        生成AI响应
        
        Args:
            messages: 消息列表
            task_type: 任务类型
            user_preference: 用户偏好模型
            stream: 是否流式响应
            **kwargs: 其他参数
            
        Returns:
            ModelResponse对象
        """
        import time
        start_time = time.time()
        
        try:
            # 选择最佳模型
            provider, model = self.select_best_model(task_type, user_preference)
            adapter = self.adapters[provider]
            
            logger.info(f"🤖 使用模型: {provider.value}/{model}")
            
            # 调用模型生成响应
            if stream:
                return await self._generate_stream_response(
                    adapter, model, messages, **kwargs
                )
            else:
                response = await adapter.generate_response(
                    model=model,
                    messages=messages,
                    **kwargs
                )
                
                latency = time.time() - start_time
                
                return ModelResponse(
                    content=response.content,
                    model=model,
                    provider=provider,
                    tokens_used=response.tokens_used,
                    cost=self._calculate_cost(provider, model, response.tokens_used),
                    latency=latency,
                    metadata=response.metadata
                )
                
        except Exception as e:
            logger.error(f"❌ 模型响应生成失败: {e}")
            # 尝试降级到备用模型
            return await self._fallback_response(messages, task_type, **kwargs)
    
    async def _generate_stream_response(
        self,
        adapter: Any,
        model: str,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """生成流式响应"""
        async for chunk in adapter.generate_stream_response(
            model=model,
            messages=messages,
            **kwargs
        ):
            yield chunk
    
    async def _fallback_response(
        self,
        messages: List[Dict[str, str]],
        task_type: TaskType,
        **kwargs
    ) -> ModelResponse:
        """降级响应处理"""
        # 尝试使用备用模型
        fallback_models = [
            (ModelProvider.OPENAI, "gpt-4o-mini"),
            (ModelProvider.LOCAL, "llama3"),
        ]
        
        for provider, model in fallback_models:
            if provider in self.adapters:
                try:
                    adapter = self.adapters[provider]
                    response = await adapter.generate_response(
                        model=model,
                        messages=messages,
                        **kwargs
                    )
                    
                    return ModelResponse(
                        content=response.content,
                        model=model,
                        provider=provider,
                        tokens_used=response.tokens_used,
                        cost=0.0,  # 降级模式不计费
                        latency=0.0,
                        metadata={"fallback": True}
                    )
                except Exception as e:
                    logger.warning(f"⚠️ 备用模型 {provider}/{model} 也失败: {e}")
                    continue
        
        # 如果所有模型都失败，返回错误响应
        return ModelResponse(
            content="抱歉，AI服务暂时不可用，请稍后重试。",
            model="error",
            provider=ModelProvider.LOCAL,
            tokens_used=0,
            cost=0.0,
            latency=0.0,
            metadata={"error": True}
        )
    
    def _is_model_healthy(self, provider: ModelProvider, model: str) -> bool:
        """检查模型健康状态"""
        key = f"{provider.value}/{model}"
        return self.model_health.get(key, True)
    
    def _get_model_cost(self, provider: ModelProvider, model: str) -> float:
        """获取模型成本（每1K tokens）"""
        cost_mapping = {
            (ModelProvider.OPENAI, "gpt-4o"): 0.03,
            (ModelProvider.OPENAI, "gpt-4o-mini"): 0.0015,
            (ModelProvider.ANTHROPIC, "claude-3-5-sonnet-20241022"): 0.03,
            (ModelProvider.GOOGLE, "gemini-pro"): 0.001,
            (ModelProvider.LOCAL, "llama3"): 0.0,
        }
        return cost_mapping.get((provider, model), 0.01)
    
    def _calculate_cost(
        self, 
        provider: ModelProvider, 
        model: str, 
        tokens: int
    ) -> float:
        """计算使用成本"""
        cost_per_1k = self._get_model_cost(provider, model)
        return (tokens / 1000) * cost_per_1k
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {}
        
        for provider, adapter in self.adapters.items():
            try:
                status = await adapter.health_check()
                health_status[provider.value] = status
            except Exception as e:
                health_status[provider.value] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
        
        return health_status
