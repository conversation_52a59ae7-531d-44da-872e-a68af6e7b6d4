# 应用配置
PROJECT_NAME=CloudAI Assistant
VERSION=1.0.0
DEBUG=true
SECRET_KEY=your-secret-key-here

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=cloudai_assistant
POSTGRES_PORT=5432

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
ACCESS_TOKEN_EXPIRE_MINUTES=43200
REFRESH_TOKEN_EXPIRE_MINUTES=43200
ALGORITHM=HS256

# AI模型API密钥
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key

# 模型配置
DEFAULT_MODEL=gpt-4
MAX_TOKENS=4096
TEMPERATURE=0.7

# 文件存储配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET=cloudai-files

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# 日志配置
LOG_LEVEL=INFO

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 限流配置
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=1000

# 邮件配置
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=CloudAI Assistant

# 前端配置
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
