#!/bin/bash

echo "========================================"
echo "🚀 CloudAI Assistant 快速测试启动器"
echo "========================================"
echo

echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3，请先安装Python 3.11+"
    echo "📥 安装命令: sudo apt install python3 python3-pip"
    exit 1
fi

echo "✅ Python环境检查通过"
echo

echo "📦 安装必要依赖..."
pip3 install fastapi uvicorn python-multipart
if [ $? -ne 0 ]; then
    echo "⚠️ 依赖安装可能有问题，但继续尝试启动..."
else
    echo "✅ 依赖安装完成"
fi
echo

echo "🔧 启动测试服务器..."
echo "📡 服务地址: http://localhost:8000"
echo "💬 测试页面: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"
echo
echo "💡 提示: 按 Ctrl+C 停止服务器"
echo

python3 test_server.py

echo
echo "👋 服务器已停止"
