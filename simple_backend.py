#!/usr/bin/env python3
"""
CloudAI Assistant 简化后端服务
"""
import os
import sqlite3
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ python-dotenv 未安装，使用默认配置")

# 创建FastAPI应用
app = FastAPI(title="CloudAI Assistant", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class ChatMessage(BaseModel):
    content: str

class ChatResponse(BaseModel):
    content: str
    model: str
    timestamp: str

# 初始化SQLite数据库
def init_db():
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS conversations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_message TEXT,
            ai_response TEXT,
            timestamp TEXT
        )
    ''')
    conn.commit()
    conn.close()

# 模拟AI响应
def generate_response(message: str) -> str:
    responses = {
        "你好": "您好！我是CloudAI Assistant，很高兴为您服务！",
        "介绍": "我是一个功能强大的AI助手，可以帮您进行对话、代码分析、文件处理和任务管理。",
        "功能": "我的主要功能包括：\n1. 智能对话\n2. 代码分析\n3. 文件管理\n4. 任务规划",
        "帮助": "您可以与我自由对话，询问各种问题。这是测试版本，完整版本会集成真实的AI模型。"
    }
    
    for keyword, response in responses.items():
        if keyword in message:
            return response
    
    return f"我收到了您的消息：「{message}」\n\n这是一个测试响应。在完整版本中，这里会调用真实的AI模型来生成智能回复。"

# API路由
@app.get("/", response_class=HTMLResponse)
async def root():
    """返回完整的网页界面"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CloudAI Assistant - 云端AI助手</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; color: white; margin-bottom: 40px; }
            .header h1 { font-size: 3rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
            .header p { font-size: 1.2rem; opacity: 0.9; }

            .main-content {
                background: white;
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                margin-bottom: 30px;
            }

            .chat-section { margin-bottom: 40px; }
            .chat-container {
                border: 2px solid #e1e5e9;
                border-radius: 15px;
                height: 400px;
                display: flex;
                flex-direction: column;
                background: #f8f9fa;
            }
            .messages {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                gap: 15px;
            }
            .message {
                max-width: 80%;
                padding: 12px 18px;
                border-radius: 18px;
                word-wrap: break-word;
                animation: fadeIn 0.3s ease-in;
            }
            .user-message {
                background: #007bff;
                color: white;
                align-self: flex-end;
                border-bottom-right-radius: 5px;
            }
            .ai-message {
                background: white;
                border: 1px solid #e1e5e9;
                align-self: flex-start;
                border-bottom-left-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }
            .input-area {
                padding: 20px;
                border-top: 1px solid #e1e5e9;
                display: flex;
                gap: 10px;
                background: white;
                border-bottom-left-radius: 15px;
                border-bottom-right-radius: 15px;
            }
            .input-area input {
                flex: 1;
                padding: 12px 18px;
                border: 2px solid #e1e5e9;
                border-radius: 25px;
                outline: none;
                font-size: 16px;
                transition: border-color 0.3s;
            }
            .input-area input:focus { border-color: #007bff; }
            .input-area button {
                padding: 12px 24px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 25px;
                cursor: pointer;
                font-weight: 600;
                transition: background-color 0.3s;
            }
            .input-area button:hover { background: #0056b3; }
            .input-area button:disabled { background: #6c757d; cursor: not-allowed; }

            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 40px;
            }
            .feature-card {
                background: white;
                padding: 25px;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                transition: transform 0.3s, box-shadow 0.3s;
            }
            .feature-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            }
            .feature-icon { font-size: 3rem; margin-bottom: 15px; }
            .feature-card h3 { margin-bottom: 10px; color: #333; }
            .feature-card p { color: #666; line-height: 1.6; }

            .status-bar {
                background: #d4edda;
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 30px;
                border-left: 4px solid #28a745;
            }
            .api-info {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                border: 1px solid #e1e5e9;
            }
            .api-info h3 { margin-bottom: 15px; color: #333; }
            .api-info code {
                background: #e9ecef;
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
            }

            @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
            @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
            .loading { animation: pulse 1.5s infinite; }

            @media (max-width: 768px) {
                .header h1 { font-size: 2rem; }
                .container { padding: 10px; }
                .main-content { padding: 20px; }
                .message { max-width: 90%; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 CloudAI Assistant</h1>
                <p>功能强大的云端AI助手平台</p>
            </div>

            <div class="status-bar">
                ✅ 服务器运行正常 | 📡 API地址: <a href="/docs" target="_blank" style="color: #007bff;">/docs</a> | 🔧 健康检查: <a href="/health" target="_blank" style="color: #007bff;">/health</a>
            </div>

            <div class="main-content">
                <div class="chat-section">
                    <h2 style="margin-bottom: 20px; color: #333;">💬 AI智能对话</h2>
                    <div class="chat-container">
                        <div class="messages" id="messages">
                            <div class="message ai-message">
                                🤖 欢迎使用CloudAI Assistant！我是您的AI助手，可以帮您解答问题、分析代码、处理文档等。请随时与我对话！
                            </div>
                        </div>
                        <div class="input-area">
                            <input type="text" id="messageInput" placeholder="输入您的消息..." onkeypress="if(event.key==='Enter') sendMessage()">
                            <button onclick="sendMessage()" id="sendBtn">发送</button>
                        </div>
                    </div>
                </div>

                <div class="features">
                    <div class="feature-card">
                        <div class="feature-icon">🧠</div>
                        <h3>智能对话</h3>
                        <p>与多个AI模型进行自然对话，获得专业的回答和建议</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💻</div>
                        <h3>代码分析</h3>
                        <p>智能代码理解、重构和生成，提升开发效率</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📁</div>
                        <h3>文件管理</h3>
                        <p>上传、管理和处理各种类型的文件</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">✅</div>
                        <h3>任务管理</h3>
                        <p>创建、跟踪和管理项目任务，提高工作效率</p>
                    </div>
                </div>

                <div class="api-info">
                    <h3>🔧 API接口信息</h3>
                    <p><strong>聊天接口:</strong> <code>POST /api/v1/chat</code></p>
                    <p><strong>对话历史:</strong> <code>GET /api/v1/conversations</code></p>
                    <p><strong>健康检查:</strong> <code>GET /health</code></p>
                    <p><strong>API文档:</strong> <a href="/docs" target="_blank" style="color: #007bff;">Swagger UI</a></p>
                    <p style="margin-top: 10px;"><strong>测试命令:</strong></p>
                    <code style="display: block; margin-top: 5px; padding: 10px; background: #e9ecef;">
                        curl -X POST http://localhost:8000/api/v1/chat -H "Content-Type: application/json" -d '{"content": "你好"}'
                    </code>
                </div>
            </div>
        </div>

        <script>
            let isLoading = false;

            async function sendMessage() {
                if (isLoading) return;

                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (!message) return;

                const messagesDiv = document.getElementById('messages');
                const sendBtn = document.getElementById('sendBtn');

                // 显示用户消息
                const userMsg = document.createElement('div');
                userMsg.className = 'message user-message';
                userMsg.innerHTML = `👤 ${message}`;
                messagesDiv.appendChild(userMsg);

                input.value = '';
                isLoading = true;
                sendBtn.disabled = true;
                sendBtn.textContent = '发送中...';

                // 显示加载状态
                const loadingMsg = document.createElement('div');
                loadingMsg.className = 'message ai-message loading';
                loadingMsg.innerHTML = '🤖 AI正在思考中...';
                loadingMsg.id = 'loading-msg';
                messagesDiv.appendChild(loadingMsg);

                messagesDiv.scrollTop = messagesDiv.scrollHeight;

                try {
                    const response = await fetch('/api/v1/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ content: message })
                    });

                    const data = await response.json();

                    // 移除加载消息
                    document.getElementById('loading-msg').remove();

                    // 显示AI响应
                    const aiMsg = document.createElement('div');
                    aiMsg.className = 'message ai-message';
                    aiMsg.innerHTML = `🤖 ${data.content.replace(/\\n/g, '<br>')}`;
                    messagesDiv.appendChild(aiMsg);

                } catch (error) {
                    document.getElementById('loading-msg').innerHTML = '🤖 ❌ 连接失败，请检查服务器状态';
                    document.getElementById('loading-msg').classList.remove('loading');
                }

                isLoading = false;
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }

            // 自动聚焦输入框
            document.getElementById('messageInput').focus();
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/api/v1/chat", response_model=ChatResponse)
async def chat(message: ChatMessage):
    try:
        # 生成响应
        ai_response = generate_response(message.content)
        
        # 保存到数据库
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO conversations (user_message, ai_response, timestamp) VALUES (?, ?, ?)",
            (message.content, ai_response, datetime.now().isoformat())
        )
        conn.commit()
        conn.close()
        
        return ChatResponse(
            content=ai_response,
            model="test-model",
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/conversations")
async def get_conversations():
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM conversations ORDER BY id DESC LIMIT 10")
    conversations = cursor.fetchall()
    conn.close()
    
    return {"conversations": [
        {"id": c[0], "user_message": c[1], "ai_response": c[2], "timestamp": c[3]}
        for c in conversations
    ]}

if __name__ == "__main__":
    print("🚀 启动CloudAI Assistant后端服务...")
    init_db()
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
