"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import auth, users, conversations, files, tasks, ai_models

api_router = APIRouter()

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 用户管理路由
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])

# 对话管理路由
api_router.include_router(conversations.router, prefix="/conversations", tags=["对话管理"])

# 文件管理路由
api_router.include_router(files.router, prefix="/files", tags=["文件管理"])

# 任务管理路由
api_router.include_router(tasks.router, prefix="/tasks", tags=["任务管理"])

# AI模型路由
api_router.include_router(ai_models.router, prefix="/ai", tags=["AI模型"])
