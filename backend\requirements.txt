# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# 缓存和任务队列
redis==5.0.1
celery==5.3.4

# AI模型集成
openai==1.3.7
anthropic==0.7.7
google-generativeai==0.3.2
langchain==0.0.350
langchain-openai==0.0.2
langchain-anthropic==0.0.1

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# 数据处理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# 文件处理
python-magic==0.4.27
Pillow==10.1.0
PyPDF2==3.0.1
python-docx==1.1.0

# 工具库
loguru==0.7.2
rich==13.7.0
typer==0.9.0
click==8.1.7

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# WebSocket支持
websockets==12.0
python-socketio==5.10.0

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0

# 文件存储
minio==7.2.0
boto3==1.34.0

# 其他工具
python-slugify==8.0.1
email-validator==2.1.0
jinja2==3.1.2
