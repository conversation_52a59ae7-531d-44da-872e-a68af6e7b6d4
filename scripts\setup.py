#!/usr/bin/env python3
"""
CloudAI Assistant 项目设置脚本
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command: str, cwd: str = None) -> bool:
    """运行命令并返回是否成功"""
    try:
        # Windows下特殊处理
        if sys.platform == "win32":
            result = subprocess.run(
                command,
                cwd=cwd,
                check=True,
                capture_output=True,
                text=True,
                shell=True
            )
        else:
            result = subprocess.run(
                command.split(),
                cwd=cwd,
                check=True,
                capture_output=True,
                text=True
            )
        print(f"✅ {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {command} 失败: {e.stderr}")
        return False
    except FileNotFoundError as e:
        print(f"❌ {command} 失败: 命令未找到")
        return False


def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    requirements = {
        "python": "python --version",
        "node": "node --version",
        "npm": "npm --version",
        "docker": "docker --version",
        "docker-compose": "docker-compose --version"
    }
    
    missing = []
    for name, command in requirements.items():
        if not run_command(command):
            missing.append(name)
    
    if missing:
        print(f"❌ 缺少以下依赖: {', '.join(missing)}")
        print("请安装缺少的依赖后重新运行此脚本")
        return False
    
    print("✅ 所有系统要求已满足")
    return True


def setup_environment():
    """设置环境变量"""
    print("⚙️ 设置环境变量...")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ 已创建 .env 文件")
        print("⚠️ 请编辑 .env 文件，填入您的API密钥")
    else:
        print("ℹ️ .env 文件已存在")


def setup_backend():
    """设置后端环境"""
    print("🐍 设置后端环境...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend 目录不存在")
        return False
    
    # 创建虚拟环境
    venv_path = backend_dir / "venv"
    if not venv_path.exists():
        if not run_command("python -m venv venv", str(backend_dir)):
            return False
    
    # 激活虚拟环境并安装依赖
    if sys.platform == "win32":
        pip_path = venv_path / "Scripts" / "pip"
    else:
        pip_path = venv_path / "bin" / "pip"
    
    if not run_command(f"{pip_path} install -r requirements.txt", str(backend_dir)):
        return False
    
    print("✅ 后端环境设置完成")
    return True


def setup_frontend():
    """设置前端环境"""
    print("⚛️ 设置前端环境...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend 目录不存在")
        return False
    
    # 安装依赖
    if not run_command("npm install", str(frontend_dir)):
        return False
    
    print("✅ 前端环境设置完成")
    return True


def setup_database():
    """设置数据库"""
    print("🗄️ 设置数据库...")
    
    # 启动数据库服务
    if not run_command("docker-compose up -d postgres redis minio"):
        return False
    
    print("✅ 数据库服务已启动")
    return True


def main():
    """主函数"""
    print("🚀 CloudAI Assistant 项目设置")
    print("=" * 50)
    
    # 检查系统要求
    if not check_requirements():
        sys.exit(1)
    
    # 设置环境变量
    setup_environment()
    
    # 设置后端
    if not setup_backend():
        print("❌ 后端设置失败")
        sys.exit(1)
    
    # 设置前端
    if not setup_frontend():
        print("❌ 前端设置失败")
        sys.exit(1)
    
    # 设置数据库
    if not setup_database():
        print("❌ 数据库设置失败")
        sys.exit(1)
    
    print("\n🎉 项目设置完成！")
    print("\n📋 下一步:")
    print("1. 编辑 .env 文件，填入您的API密钥")
    print("2. 运行 'python scripts/start.py' 启动服务")
    print("3. 访问 http://localhost:3000 查看前端")
    print("4. 访问 http://localhost:8000/docs 查看API文档")


if __name__ == "__main__":
    main()
